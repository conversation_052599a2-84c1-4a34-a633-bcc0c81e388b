using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Threading.Tasks;
using App.Base.Middleware;
using App.Base.Utilities;
using App.ECommerce.MockData;
using App.ECommerce.ProcessFlow.Interface;
using App.ECommerce.Repository.Entities;
using App.ECommerce.Repository.Interface;
using App.ECommerce.Resource.Dtos;
using App.ECommerce.Resource.Dtos.InputDtos;
using App.ECommerce.Resource.Model;
using App.ECommerce.Services.UploadStore;
using App.ECommerce.Setting;
using App.ECommerce.Units;
using App.ECommerce.Units.Attribute;
using App.ECommerce.Units.Consts;
using App.ECommerce.Units.Enums;
using log4net;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Localization;
using Action = App.ECommerce.Resource.Model.Action;

namespace App.ECommerce.Controllers.API;

[ApiController]
[Produces("application/json")]
[Route(RoutePrefix.API_PARTNER)]
[ApiExplorerSettings(GroupName = "partner-v1")]
[Authorize(AuthenticationSchemes = JwtBearerDefaults.AuthenticationScheme)]
[MultiPolicysAuthorize(Policys = RolePrefix.Partner, Rules = "")]
public class FileManageController : BaseController
{
    private readonly ILog _log4net = log4net.LogManager.GetLogger(typeof(FileManageController));
    private readonly IServiceScopeFactory _serviceScopeFactory;
    private readonly IPartnerRepository _partnerRepository;
    private readonly IShopRepository _shopRepository;
    private readonly IGroupFileRepository _groupFileRepository;
    private readonly ITempFilesFlow _tempFilesFlow;

    public FileManageController(
        IStringLocalizer localizer,
        IServiceScopeFactory serviceScopeFactory,
        IPartnerRepository partnerRepository,
        IShopRepository shopRepository, 
        IGroupFileRepository groupFileRepository,
        ITempFilesFlow tempFilesFlow
    ) : base(localizer)
    {
        _serviceScopeFactory = serviceScopeFactory;
        _partnerRepository = partnerRepository;
        _shopRepository = shopRepository;
        _groupFileRepository = groupFileRepository;
        _tempFilesFlow = tempFilesFlow;
    }

    /// <summary>
    /// Get list groups
    /// </summary>
    /// <param name="model"></param>
    /// <returns>Result list GroupFile for Partner</returns>
    // GET: api/partner/FileManage/ListGroup
    [HttpGet("ListGroup")]
    public async Task<IActionResult> ListGroup([FromQuery] FileGroupFilterDto model)
    {
        string requestId = Guid.NewGuid().ToString();
        try
        {
            string partnerId = GetUserIdAuth();
            Partner partner = await _partnerRepository.FindByPartnerId(partnerId);
            if (partner == null) return ResponseUnauthorized(new CustomBadRequest(localizer("BASE_USER_AUTH_NOT_FOUND"), this.ControllerContext));

            var warehouseTotal = _groupFileRepository.TotalGroupFile(partner.PartnerId, model.ShopId);
            if (warehouseTotal == 0)
            {
                FileManageData.LoadDefaultData(model.ShopId, _groupFileRepository);
            }

            Paging paging = new Paging()
            {
                Search = $"{model.Search}",
                PageIndex = model.Skip / (model.Limit == 0 ? 1 : model.Limit),
                PageSize = model.Limit,
                NameType = TypeSortName.Created,
                SortType = TypeSort.asc
            };

            PagingResult<GroupFile> listGroup = _groupFileRepository.ListGroupFile(paging, partner.PartnerId);
            List<GroupFileDto> listGroupDto = _mapper.Map<List<GroupFileDto>>(listGroup.Result);

            foreach (var groupDto in listGroupDto)
            {
                groupDto.NumberFile = _groupFileRepository.TotalMediaFile(partner.PartnerId, model.ShopId, groupDto.GroupFileId);
            }

            return ResponseData(new { data = listGroupDto, skip = model.Skip, limit = model.Limit, total = listGroup.Total });
        }
        catch (Exception ex)
        {
            LogEvent(new EventLogDto
            {
                RefId = "",
                RefType = TypeFor.Partner,
                RequestId = requestId,
                Action = LogActionEnum.Load,
                Status = LogStatusEnum.Error,
                ActionAPI = $"{RoutePrefix.PARTNER}/FileManage/ListGroup",
                Message = $"Error Partner get list GroupFile",
                Exception = ex,
                DataObject = null
            });

            return LogExceptionEvent(_log4net, $"{RoutePrefix.PARTNER}/FileManage/ListGroup", ex);
        }
    }

    /// <summary>
    /// Create group
    /// </summary>
    /// <param name="model"></param>
    /// <returns>The result CreateGroup</returns>
    // POST: api/partner/FileManage/CreateGroup
    [HttpPost("CreateGroup")]
    public async Task<IActionResult> CreateGroup(GroupFileDto model)
    {
        string requestId = Guid.NewGuid().ToString();
        try
        {
            string partnerId = GetUserIdAuth();
            Partner partner = await _partnerRepository.FindByPartnerId(partnerId);
            if (partner == null) return ResponseUnauthorized(new CustomBadRequest(localizer("BASE_USER_AUTH_NOT_FOUND"), this.ControllerContext));
            if (model.PartnerId != partner.PartnerId) return ResponseBadRequest(new CustomBadRequest(localizer("FM_GROUP_NOT_YOURS"), this.ControllerContext));

            GroupFile group = _mapper.Map<GroupFile>(model);
            group.PartnerId = partner.PartnerId;
            group = _groupFileRepository.CreateGroupFile(group);
            GroupFileDto groupDto = _mapper.Map<GroupFileDto>(group);

            LogEvent(new EventLogDto
            {
                RefId = "",
                RefType = TypeFor.Partner,
                RequestId = requestId,
                Action = LogActionEnum.Create,
                Status = LogStatusEnum.Success,
                ActionAPI = $"{RoutePrefix.PARTNER}/FileManage/CreateGroup",
                Message = $"Partner get list GroupFile",
                Exception = null,
                DataObject = model
            });
            return ResponseData(groupDto);
        }
        catch (Exception ex)
        {
            LogEvent(new EventLogDto
            {
                RefId = "",
                RefType = TypeFor.Partner,
                RequestId = requestId,
                Action = LogActionEnum.Create,
                Status = LogStatusEnum.Error,
                ActionAPI = $"{RoutePrefix.PARTNER}/FileManage/CreateGroup",
                Message = $"Error Partner create GroupFile",
                Exception = ex,
                DataObject = model
            });

            return LogExceptionEvent(_log4net, $"{RoutePrefix.PARTNER}/FileManage/CreateGroup", ex, model);
        }
    }

    /// <summary>
    /// Update group
    /// </summary>
    /// <param name="model"></param>
    /// <returns>The result update GroupFile</returns>
    // PUT: api/partner/FileManage/UpdateGroup
    [HttpPut("UpdateGroup")]
    public async Task<IActionResult> UpdateGroup(GroupFileDto model)
    {
        string requestId = Guid.NewGuid().ToString();
        try
        {
            string partnerId = GetUserIdAuth();
            Partner partner = await _partnerRepository.FindByPartnerId(partnerId);
            if (partner == null) return ResponseUnauthorized(new CustomBadRequest(localizer("BASE_USER_AUTH_NOT_FOUND"), this.ControllerContext));

            // Validate
            GroupFile? group = _groupFileRepository.FindByGroupFileId(model.GroupFileId);
            if (group == null) return ResponseBadRequest(new CustomBadRequest(localizer("FM_GROUP_NOT_FOUND"), this.ControllerContext));
            if (group.PartnerId != partner.PartnerId) return ResponseBadRequest(new CustomBadRequest(localizer("FM_GROUP_NOT_YOURS"), this.ControllerContext));

            // group.GroupFileId = model.GroupFileId;
            // group.PartnerId = model.PartnerId;
            group.GroupName = model.GroupName;
            group.Status = model.Status;
            group.Created = DateTimes.Now();
            group.Updated = DateTimes.Now();
            group = _groupFileRepository.UpdateGroupFile(group);
            GroupFileDto groupDto = _mapper.Map<GroupFileDto>(group);

            LogEvent(new EventLogDto
            {
                RefId = "",
                RefType = TypeFor.Partner,
                RequestId = requestId,
                Action = LogActionEnum.Update,
                Status = LogStatusEnum.Success,
                ActionAPI = $"{RoutePrefix.PARTNER}/FileManage/UpdateGroup",
                Message = $"Partner update GroupFile",
                Exception = null,
                DataObject = model
            });

            return ResponseData(groupDto);
        }
        catch (Exception ex)
        {
            LogEvent(new EventLogDto
            {
                RefId = "",
                RefType = TypeFor.Partner,
                RequestId = requestId,
                Action = LogActionEnum.Update,
                Status = LogStatusEnum.Error,
                ActionAPI = $"{RoutePrefix.PARTNER}/FileManage/UpdateGroup",
                Message = $"Error Partner update GroupFile",
                Exception = ex,
                DataObject = model
            });

            return LogExceptionEvent(_log4net, $"{RoutePrefix.PARTNER}/FileManage/UpdateGroup", ex, model);
        }
    }

    /// <summary>
    /// Update group
    /// </summary>
    /// <param name="shopId"></param>
    /// <param name="groupFileId"></param>
    /// <returns>The result update GroupFile</returns>
    // GET: api/partner/FileManage/DetailGroup
    [HttpGet("DetailGroup")]
    public async Task<IActionResult> DetailGroup([FromQuery] string shopId, [FromQuery] string groupFileId)
    {
        string requestId = Guid.NewGuid().ToString();
        try
        {
            string partnerId = GetUserIdAuth();
            Partner partner = await _partnerRepository.FindByPartnerId(partnerId);
            if (partner == null) return ResponseUnauthorized(new CustomBadRequest(localizer("BASE_USER_AUTH_NOT_FOUND"), this.ControllerContext));
            
            Shop? shop = _shopRepository.FindByShopId(shopId);
            if (shop == null) return ResponseBadRequest(new CustomBadRequest(localizer("SHOP_NOT_FOUND"), this.ControllerContext));
            if (shop.PartnerId != partner.PartnerId && shop.PartnerId != partner.ParentId) return ResponseBadRequest(new CustomBadRequest(localizer("SHOP_NOT_YOURS"), this.ControllerContext));

            // Validate
            GroupFile? group = _groupFileRepository.FindByGroupFileId(groupFileId);
            if (group == null) return ResponseBadRequest(new CustomBadRequest(localizer("FM_GROUP_NOT_FOUND"), this.ControllerContext));
            if (group.PartnerId != partner.PartnerId) return ResponseBadRequest(new CustomBadRequest(localizer("FM_GROUP_NOT_YOURS"), this.ControllerContext));

            GroupFileDto groupDto = _mapper.Map<GroupFileDto>(group);

            return ResponseData(new
            {
                Timestamp = DateTimes.Now(),
                Message = localizer("SUCCESS"),
                Data = groupDto
            });
        }
        catch (Exception ex)
        {
            LogEvent(new EventLogDto
            {
                RefId = "",
                RefType = TypeFor.Partner,
                RequestId = requestId,
                Action = LogActionEnum.Update,
                Status = LogStatusEnum.Error,
                ActionAPI = $"{RoutePrefix.PARTNER}/FileManage/DetailGroup",
                Message = $"Error Partner detail GroupFile",
                Exception = ex,
                DataObject = groupFileId
            });

            return StatusCode(500, new
            {
                Timestamp = DateTimes.Now(),
                Message = localizer("FAIL"),
                Data = (object)null
            });
        }
    }

    /// <summary>
    /// Delete group
    /// </summary>
    /// <param name="groupFileId"></param>
    /// <returns>The result delete GroupFile</returns>
    // DELETE: api/partner/FileManage/DeleteGroup
    [HttpDelete("DeleteGroup")]
    [MultiPolicysAuthorizeAttribute(Policys = RolePrefix.Partner, Rules = "")]
    public async Task<IActionResult> DeleteGroup(string groupFileId)
    {
        string requestId = Guid.NewGuid().ToString();
        try
        {
            string partnerId = GetUserIdAuth();
            Partner partner = await _partnerRepository.FindByPartnerId(partnerId);
            if (partner == null) return ResponseUnauthorized(new CustomBadRequest(localizer("BASE_USER_AUTH_NOT_FOUND"), this.ControllerContext));

            GroupFile? group = _groupFileRepository.FindByGroupFileId(groupFileId);
            if (group == null) return ResponseBadRequest(new CustomBadRequest(localizer("FM_GROUP_NOT_FOUND"), this.ControllerContext));
            if (group.PartnerId != partner.PartnerId) return ResponseBadRequest(new CustomBadRequest(localizer("FM_GROUP_NOT_YOURS"), this.ControllerContext));

            PagingResult<MediaFile> listFile = _groupFileRepository.ListMediaFileById(Constants.MaxPaging, group.GroupFileId);
            foreach (var mediaFile in listFile.Result)
            {
                await S3Upload.DeleteImageS3(new List<string> { mediaFile.Link });
                _groupFileRepository.DeleteMediaFile(mediaFile.MediaFileId);
            }

            _groupFileRepository.DeleteGroupFile(group.GroupFileId);

            LogEvent(new EventLogDto
            {
                RefId = "",
                RefType = TypeFor.Partner,
                RequestId = requestId,
                Action = LogActionEnum.Delete,
                Status = LogStatusEnum.Success,
                ActionAPI = $"{RoutePrefix.PARTNER}/FileManage/DeleteGroup",
                Message = $"Partner delete GroupFile",
                Exception = null,
                DataObject = groupFileId
            });

            return ResponseData(new { Timestamp = DateTimes.Now(), Result = true, Message = localizer("FM_GROUP_DELETE_SUCCESS") });
        }
        catch (Exception ex)
        {
            LogEvent(new EventLogDto
            {
                RefId = "",
                RefType = TypeFor.Partner,
                RequestId = requestId,
                Action = LogActionEnum.Delete,
                Status = LogStatusEnum.Error,
                ActionAPI = $"{RoutePrefix.PARTNER}/FileManage/DeleteGroup",
                Message = $"Error Partner delete GroupFile",
                Exception = ex,
                DataObject = groupFileId
            });

            return LogExceptionEvent(_log4net, $"{RoutePrefix.PARTNER}/FileManage/DeleteGroup", ex);
        }
    }

    /// <summary>
    /// Get list file from group
    /// </summary>
    /// <param name="model"></param>
    /// <returns>Result list MediaFile for group</returns>
    // POST: api/partner/FileManage/ListFile
    [HttpGet("ListFile")]
    public async Task<IActionResult> ListFile([FromQuery] FileFilterDto model)
    {
        string requestId = Guid.NewGuid().ToString();
        try
        {
            string partnerId = GetUserIdAuth();
            Partner partner = await _partnerRepository.FindByPartnerId(partnerId);
            if (partner == null) return ResponseUnauthorized(new CustomBadRequest(localizer("BASE_USER_AUTH_NOT_FOUND"), this.ControllerContext));
            // if (string.IsNullOrEmpty(model.GroupFileId)) return ResponseBadRequest(new CustomBadRequest(localizer("FM_GROUP_NOT_FOUND"), this.ControllerContext));

            Paging paging = new Paging()
            {
                Search = $"{model.Search}",
                PageIndex = model.Skip / (model.Limit == 0 ? 1 : model.Limit),
                PageSize = model.Limit,
                NameType = TypeSortName.Created,
                SortType = TypeSort.desc
            };
            
            model.PartnerId = partner.PartnerId;

            PagingResult<MediaFile> listFile = _groupFileRepository.ListMediaFile(paging, model);
            List<MediaFileDto> listFileDto = _mapper.Map<List<MediaFileDto>>(listFile.Result);

            return ResponseData(new { data = listFileDto, skip = model.Skip, limit = model.Limit, total = listFile.Total });
        }
        catch (Exception ex)
        {
            LogEvent(new EventLogDto
            {
                RefId = "",
                RefType = TypeFor.Partner,
                RequestId = requestId,
                Action = LogActionEnum.Load,
                Status = LogStatusEnum.Error,
                ActionAPI = $"{RoutePrefix.PARTNER}/FileManage/ListFile",
                Message = $"Error Partner get list MediaFile",
                Exception = ex,
                DataObject = null
            });

            return LogExceptionEvent(_log4net, $"{RoutePrefix.PARTNER}/FileManage/ListFile", ex);
        }
    }

    /// <summary>
    /// Create file
    /// </summary>
    /// <param name="model"></param>
    /// <returns>The result CreateFile</returns>
    // PUT: api/partner/FileManage/CreateFile
    [HttpPost("CreateFile")]
    public async Task<IActionResult> CreateFile(FileDto model)
    {
        string requestId = Guid.NewGuid().ToString();
        try
        {
            string partnerId = GetUserIdAuth();
            Partner partner = await _partnerRepository.FindByPartnerId(partnerId);
            if (partner == null) return ResponseUnauthorized(new CustomBadRequest(localizer("BASE_USER_AUTH_NOT_FOUND"), this.ControllerContext));

            if (!string.IsNullOrEmpty(model.GroupFileId)) {
                GroupFile? groupFile = _groupFileRepository.FindByGroupFileId(model.GroupFileId);
                if (groupFile == null) return ResponseBadRequest(new CustomBadRequest(localizer("FM_GROUP_NOT_FOUND"), this.ControllerContext));
                if (groupFile.PartnerId != partner.PartnerId) return ResponseBadRequest(new CustomBadRequest(localizer("FM_GROUP_NOT_YOURS"), this.ControllerContext));
            }
            else {
                string groupFileId = _groupFileRepository.GetDefaultGroupFileId(partner.PartnerId);

                if (string.IsNullOrEmpty(groupFileId)) {
                    var warehouseTotal = _groupFileRepository.TotalGroupFile(partner.PartnerId, model.ShopId);
                    if (warehouseTotal == 0)
                        FileManageData.LoadDefaultData(model.ShopId, _groupFileRepository);

                    groupFileId = _groupFileRepository.GetDefaultGroupFileId(partner.PartnerId);
                }

                model.GroupFileId = groupFileId;
            }

            if (model.FileUpload == null) return ResponseBadRequest(new CustomBadRequest(localizer("FILE_UPLOAD_NOT_FOUND"), this.ControllerContext));
            if (model.FileUpload.Length == 0) return ResponseBadRequest(new CustomBadRequest(localizer("FILE_UPLOAD_NOT_FOUND"), this.ControllerContext));

            var fileName = model.FileUpload.FileName.FixFileName();
            var (maxImageSize, maxVideoSize) = GetMaxFileSizeByType(model.RefType);
            long maxSize = 0;
            if (fileName.IsValidImageExtension())
                maxSize = maxImageSize;
            else if (fileName.IsValidVideoExtension())
                maxSize = maxVideoSize;
            else
                return ResponseBadRequest(new CustomBadRequest(localizer("FILE_TYPE_NOT_SUPPORTED"), this.ControllerContext));

            if (model.FileUpload.Length > maxSize)
                return ResponseBadRequest(new CustomBadRequest(localizer("FILE_UPLOAD_MAX_SIZE"), this.ControllerContext));

            MediaFile file = new MediaFile
            {
                PartnerId = partner.PartnerId,
                ShopId = model.ShopId,
                GroupFileId = model.GroupFileId,
                Type = TypeMedia.IMAGE,
                Source = BaseSourceEnum.Manual,
                RefType = model.RefType,
                RefId = model.RefId,
                Link = "",
            };

            if (model.RefType == RefTypeEnum.Product || model.RefType == RefTypeEnum.Service)
            {
                long totalMediaFile = _groupFileRepository.TotalMediaFileByRef(model.ShopId, model.RefType, model.RefId);
                if (totalMediaFile >= CommonConst.MAX_IMAGE_TEMP_COUNT)
                    return ResponseBadRequest(new CustomBadRequest(localizer("FM_FILE_MAX_COUNT"), this.ControllerContext));
            }

            string basePath = _tempFilesFlow.GetBasePathByRefType(model.RefType);
            string path = $"{basePath}{model.ShopId}";

            var keyFile = S3Upload.SendMyFileToS3(model.FileUpload, fileName, path).Result;
            if (!string.IsNullOrEmpty(keyFile))
            {
                file.Type = fileName.IsValidImageExtension() ? TypeMedia.IMAGE : fileName.IsValidVideoExtension() ? TypeMedia.VIDEO : TypeMedia.MEDIA;
                file.Link = $"{keyFile}";
            }

            file = _groupFileRepository.CreateMediaFile(file);
            MediaFileDto fileDto = _mapper.Map<MediaFileDto>(file);

            LogEvent(new EventLogDto
            {
                RefId = "",
                RefType = TypeFor.Partner,
                RequestId = requestId,
                Action = LogActionEnum.Create,
                Status = LogStatusEnum.Success,
                ActionAPI = $"{RoutePrefix.PARTNER}/FileManage/CreateFile",
                Message = $"Partner create MediaFile",
                Exception = null,
                DataObject = fileDto
            });

            return ResponseData(fileDto);
        }
        catch (Exception ex)
        {
            LogEvent(new EventLogDto
            {
                RefId = "",
                RefType = TypeFor.Partner,
                RequestId = requestId,
                Action = LogActionEnum.Create,
                Status = LogStatusEnum.Error,
                ActionAPI = $"{RoutePrefix.PARTNER}/FileManage/CreateFile",
                Message = $"Error Partner create MediaFile",
                Exception = ex,
                DataObject = model
            });

            return LogExceptionEvent(_log4net, $"{RoutePrefix.PARTNER}/FileManage/CreateFile", ex, model);
        }
    }

    /// <summary>
    /// Delete file
    /// </summary>
    /// <param name="mediaFileId"></param>
    /// <returns>The result delete MediaFile</returns>
    // DELETE: api/partner/FileManage/DeleteFile
    [HttpDelete("DeleteFile")]
    [MultiPolicysAuthorizeAttribute(Policys = RolePrefix.Partner, Rules = "")]
    public async Task<IActionResult> DeleteFile(string mediaFileId)
    {
        string requestId = Guid.NewGuid().ToString();
        try
        {
            string partnerId = GetUserIdAuth();
            Partner partner = await _partnerRepository.FindByPartnerId(partnerId);
            if (partner == null) return ResponseUnauthorized(new CustomBadRequest(localizer("BASE_USER_AUTH_NOT_FOUND"), this.ControllerContext));

            MediaFile? file = _groupFileRepository.FindByMediaFileId(mediaFileId);
            if (file == null) return ResponseBadRequest(new CustomBadRequest(localizer("FM_FILE_NOT_FOUND"), this.ControllerContext));

            GroupFile? group = _groupFileRepository.FindByGroupFileId(file.GroupFileId);
            if (group == null) return ResponseBadRequest(new CustomBadRequest(localizer("FM_GROUP_NOT_FOUND"), this.ControllerContext));
            if (group.PartnerId != partner.PartnerId) return ResponseBadRequest(new CustomBadRequest(localizer("FM_FILE_NOT_YOURS"), this.ControllerContext));

            await S3Upload.DeleteImageS3(new List<string> { file.Link });
            _groupFileRepository.DeleteMediaFile(file.MediaFileId);

            LogEvent(new EventLogDto
            {
                RefId = "",
                RefType = TypeFor.Partner,
                RequestId = requestId,
                Action = LogActionEnum.Delete,
                Status = LogStatusEnum.Success,
                ActionAPI = $"{RoutePrefix.PARTNER}/FileManage/DeleteFile",
                Message = $"Partner delete MediaFile",
                Exception = null,
                DataObject = null
            });

            return ResponseData(new { Timestamp = DateTimes.Now(), Result = true, Message = localizer("FM_FILE_DELETE_SUCCESS") });
        }
        catch (Exception ex)
        {
            LogEvent(new EventLogDto
            {
                RefId = "",
                RefType = TypeFor.Partner,
                RequestId = requestId,
                Action = LogActionEnum.Delete,
                Status = LogStatusEnum.Error,
                ActionAPI = $"{RoutePrefix.PARTNER}/FileManage/DeleteFile",
                Message = $"Error Partner delete MediaFile",
                Exception = ex,
                DataObject = null
            });

            return LogExceptionEvent(_log4net, $"{RoutePrefix.PARTNER}/FileManage/DeleteFile", ex);
        }
    }

    private static readonly Dictionary<RefTypeEnum, (long ImageSize, long VideoSize)> MaxFileSizes = new()
    {
        { RefTypeEnum.PopupAds, (CommonConst.IMAGE_SIZE_2MB, CommonConst.IMAGE_SIZE_25MB) },
        { RefTypeEnum.Article, (CommonConst.IMAGE_SIZE_5MB, CommonConst.IMAGE_SIZE_25MB) },
        { RefTypeEnum.Product, (CommonConst.IMAGE_SIZE_2MB, CommonConst.IMAGE_SIZE_25MB) },
        { RefTypeEnum.Service, (CommonConst.IMAGE_SIZE_2MB, CommonConst.IMAGE_SIZE_25MB) },
        // TODO: Thêm các RefType khác ở đây
    };

    private static (long ImageSize, long VideoSize) GetMaxFileSizeByType(RefTypeEnum refType)
    {
        if (MaxFileSizes.TryGetValue(refType, out var sizes))
            return sizes;
        return (CommonConst.IMAGE_SIZE_2MB, 0);
    }
}
