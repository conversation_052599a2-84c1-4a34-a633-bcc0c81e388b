using App.Base.Middleware;
using App.Base.Repository.Entities;
using App.Base.Resource.Model;
using App.ECommerce.Repository.Entities;
using App.ECommerce.Repository.Interface;
using App.ECommerce.Resource.Dtos;
using App.ECommerce.Resource.Dtos.InputDtos;
using App.ECommerce.Resource.Model;
using App.ECommerce.Services.UploadStore;
using App.ECommerce.Setting;
using App.ECommerce.Units;
using App.ECommerce.Units.Consts;
using App.ECommerce.Units.Enums;

using log4net;

using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Localization;

using OfficeOpenXml;

namespace App.ECommerce.Controllers.API;

[ApiController]
[Produces("application/json")]
[Route(RoutePrefix.API_PARTNER)]
[ApiExplorerSettings(GroupName = "partner-v1")]
[Authorize(AuthenticationSchemes = JwtBearerDefaults.AuthenticationScheme)]
[MultiPolicysAuthorize(Policys = RolePrefix.Partner, Rules = "")]
public class CategoryPartnerController : BaseController
{
    private readonly ILog _log4net = log4net.LogManager.GetLogger(typeof(CategoryPartnerController));
    private readonly IServiceScopeFactory _serviceScopeFactory;

    private readonly IPartnerRepository _partnerRepository;
    private readonly IShopRepository _shopRepository;
    private readonly ICategoryRepository _categoryRepository;
    private readonly IItemsRepository _itemsRepository;
    private readonly IGroupFileRepository _groupFileRepository;
    private readonly IStorageRepository _storageRepository;

    public CategoryPartnerController(
        IStringLocalizer localizer,
        IServiceScopeFactory serviceScopeFactory,
        IPartnerRepository partnerRepository,
        IShopRepository shopRepository,
        ICategoryRepository categoryRepository,
        IItemsRepository itemsRepository,
        IGroupFileRepository groupFileRepository,
        IStorageRepository storageRepository
    ) : base(localizer)
    {
        _serviceScopeFactory = serviceScopeFactory;
        _partnerRepository = partnerRepository;
        _shopRepository = shopRepository;
        _categoryRepository = categoryRepository;
        _itemsRepository = itemsRepository;
        _groupFileRepository = groupFileRepository;
        _storageRepository = storageRepository;
    }

    /// <summary>
    /// Get tree Category for shop (Danh sách danh mục dạng cây theo loại cho cửa hàng)
    /// </summary>
    /// <param name="filter"></param>
    /// <returns>Result TreeCategory for shop</returns>
    // POST: api/partner/CategoryPartner/TreeCategory
    [HttpPost("TreeCategory")]
    public async Task<IActionResult> TreeCategory([FromBody] CategoryFilterDto filter)
    {
        string requestId = Guid.NewGuid().ToString();
        try
        {
            string partnerId = GetUserIdAuth();
            Partner partner = await _partnerRepository.FindByPartnerId(partnerId);
            if (partner == null) return ResponseUnauthorized(new CustomBadRequest(localizer("BASE_USER_AUTH_NOT_FOUND"), this.ControllerContext));
            if (filter.PartnerId != partner.PartnerId) return ResponseUnauthorized(new CustomBadRequest(localizer("BASE_USER_AUTH_NOT_FOUND"), this.ControllerContext));

            CategoryFilterDto filterCategory = new CategoryFilterDto
            {
                PartnerId = partner.PartnerId,
                ShopId = filter.ShopId,
                CategoryType = filter.CategoryType,
                Paging = Constants.MaxPaging
            };

            var categoryTree = await _categoryRepository.FindTree(filterCategory);
            categoryTree.Result = S3Upload.ChangeDomainS3(categoryTree.Result);

            return ResponseData(new { data = categoryTree });
        }
        catch (Exception ex)
        {
            LogEvent(new EventLogDto
            {
                RefId = "",
                RefType = TypeFor.Partner,
                RequestId = requestId,
                Action = LogActionEnum.Load,
                Status = LogStatusEnum.Error,
                ActionAPI = $"{RoutePrefix.PARTNER}/CategoryPartner/TreeCategory",
                Message = $"Error Partner get list Category",
                Exception = ex,
                DataObject = null
            });

            return LogExceptionEvent(_log4net, $"{RoutePrefix.PARTNER}/CategoryPartner/TreeCategory", ex);
        }
    }

    /// <summary>
    /// Get list Category for shop (Danh sách danh mục theo loại cho cửa hàng)
    /// </summary>
    /// <param name="filter"></param>
    /// <returns>Result ListCategory for shop</returns>
    // GET: api/partner/CategoryPartner/ListCategory
    [HttpGet("ListCategory")]
    public async Task<IActionResult> ListCategory([FromQuery] CategoryFilterDto filter)
    {
        string requestId = Guid.NewGuid().ToString();
        try
        {
            string partnerId = GetUserIdAuth();
            Partner partner = await _partnerRepository.FindByPartnerId(partnerId);
            if (partner == null) return ResponseUnauthorized(new CustomBadRequest(localizer("BASE_USER_AUTH_NOT_FOUND"), this.ControllerContext));
            if (filter.PartnerId != partner.PartnerId) return ResponseUnauthorized(new CustomBadRequest(localizer("BASE_USER_AUTH_NOT_FOUND"), this.ControllerContext));

            Paging paging = new Paging()
            {
                Search = $"{filter.Paging.Search}",
                PageIndex = filter.Paging.PageIndex,
                PageSize = filter.Paging.PageSize,
                NameType = TypeSortName.Created,
                SortType = TypeSort.desc
            };
            partnerId = partner.ParentId != null ? partner.ParentId : partner.PartnerId;

            filter.Paging = paging;

            PagingResult<Category> listCategory = await _categoryRepository.ListCategory(filter);

            List<CategoryDto> listCategoryDto = _mapper.Map<List<CategoryDto>>(listCategory.Result);
            List<string> ids = listCategoryDto.Select(x => x.ParentId).ToList();
            List<Category> listParent = await _categoryRepository.FindByCategoryIds(ids);
            foreach (var item in listCategoryDto)
            {
                item.ParentName = $"{(listParent.FirstOrDefault(x => x.CategoryId == item.ParentId)?.CategoryName ?? "")}";
                item.QuantityItems = _itemsRepository.TotalItems(partnerId, filter.ShopId, item.CategoryId);
            }

            return ResponseData(new
            {
                data = listCategoryDto,
                skip = filter.Paging.PageIndex,
                limit = filter.Paging.PageSize,
                total = listCategory.Total
            });
        }
        catch (Exception ex)
        {
            LogEvent(new EventLogDto
            {
                RefId = "",
                RefType = TypeFor.Partner,
                RequestId = requestId,
                Action = LogActionEnum.Load,
                Status = LogStatusEnum.Error,
                ActionAPI = $"{RoutePrefix.PARTNER}/CategoryPartner/ListCategory",
                Message = $"Error Partner get list Category",
                Exception = ex,
                DataObject = null
            });

            return LogExceptionEvent(_log4net, $"{RoutePrefix.PARTNER}/CategoryPartner/ListCategory", ex);
        }
    }

    /// <summary>
    /// Create Category for shop (Tạo mới danh mục cho cửa hàng)
    /// </summary>
    /// <param name="model"></param>
    /// <returns>The result CreateCategory for shop</returns>
    // POST: api/partner/CategoryPartner/CreateCategory
    [HttpPost("CreateCategory")]
    public async Task<IActionResult> CreateCategory(CategoryDto model)
    {
        string requestId = Guid.NewGuid().ToString();
        try
        {
            string partnerId = GetUserIdAuth();
            Partner partner = await _partnerRepository.FindByPartnerId(partnerId);
            if (partner == null) return ResponseUnauthorized(new CustomBadRequest(localizer("BASE_USER_AUTH_NOT_FOUND"), this.ControllerContext));
            if (model.PartnerId != partner.PartnerId) return ResponseUnauthorized(new CustomBadRequest(localizer("BASE_USER_AUTH_NOT_FOUND"), this.ControllerContext));

            // Validate
            Shop? shop = _shopRepository.FindByShopId(model.ShopId);
            if (shop == null) return ResponseBadRequest(new CustomBadRequest(localizer("SHOP_NOT_FOUND"), this.ControllerContext));
            if (shop.PartnerId != partner.PartnerId && shop.PartnerId != partner.ParentId) return ResponseBadRequest(new CustomBadRequest(localizer("SHOP_NOT_YOURS"), this.ControllerContext));
            if (string.IsNullOrEmpty(model.CategoryName)) return ResponseBadRequest(new CustomBadRequest(localizer("CATEGORY_NAME_INVALID"), this.ControllerContext));

            if (!string.IsNullOrEmpty(model.ParentId))
            {
                var parentCategory = await _categoryRepository.FindByCategoryId(model.ParentId);
                if (parentCategory == null) return ResponseBadRequest(new CustomBadRequest(localizer("CATEGORY_PARENT_INVALID"), this.ControllerContext));
                if (parentCategory.CategoryType != model.CategoryType) return ResponseBadRequest(new CustomBadRequest(localizer("CATEGORY_PARENT_TYPE_INVALID"), this.ControllerContext));
            }

            Category category = _mapper.Map<Category>(model);
            category.Image = model.Image ?? new MediaInfo()
            {
                MediaFileId = "",
                Type = TypeMedia.IMAGE,
                Link = Constants.IsAlwaysUseS3 ? "sample/advertise.png" : "assets/sample/advertise.png"
            };

            category.PartnerId = partner.ParentId != null ? partner.ParentId : partner.PartnerId;

            category = await _categoryRepository.CreateCategory(category);

            if (category.Image != null && !string.IsNullOrEmpty(category.Image.Link))
                _groupFileRepository.UpdateRefIdByMediaFileId(category.ShopId, category.Image.MediaFileId, category.CategoryId);

            CategoryDto categoryDto = _mapper.Map<CategoryDto>(category);

            LogEvent(new EventLogDto
            {
                RefId = "",
                RefType = TypeFor.Partner,
                RequestId = requestId,
                Action = LogActionEnum.Create,
                Status = LogStatusEnum.Error,
                ActionAPI = $"{RoutePrefix.PARTNER}/CategoryPartner/CreateCategory",
                Message = $"Partner create Category",
                Exception = null,
                DataObject = null
            });

            return ResponseData(categoryDto);
        }
        catch (Exception ex)
        {
            LogEvent(new EventLogDto
            {
                RefId = "",
                RefType = TypeFor.Partner,
                RequestId = requestId,
                Action = LogActionEnum.Create,
                Status = LogStatusEnum.Error,
                ActionAPI = $"{RoutePrefix.PARTNER}/CategoryPartner/CreateCategory",
                Message = $"Error Partner create Category",
                Exception = ex,
                DataObject = null
            });

            return LogExceptionEvent(_log4net, $"{RoutePrefix.PARTNER}/CategoryPartner/CreateCategory", ex, model);
        }
    }

    /// <summary>
    /// Update Category for shop (Cập nhật danh mục cho cửa hàng)
    /// </summary>
    /// <param name="model"></param>
    /// <returns>The result UpdateCategory for shop</returns>
    // PUT: api/partner/CategoryPartner/UpdateCategory
    [HttpPut("UpdateCategory")]
    public async Task<IActionResult> UpdateCategory(CategoryDto model)
    {
        string requestId = Guid.NewGuid().ToString();
        try
        {
            string partnerId = GetUserIdAuth();
            Partner partner = await _partnerRepository.FindByPartnerId(partnerId);
            if (partner == null) return ResponseUnauthorized(new CustomBadRequest(localizer("BASE_USER_AUTH_NOT_FOUND"), this.ControllerContext));
            if (model.PartnerId != partner.PartnerId) return ResponseUnauthorized(new CustomBadRequest(localizer("BASE_USER_AUTH_NOT_FOUND"), this.ControllerContext));

            // Validate
            Category? category = await _categoryRepository.FindByCategoryId(model.CategoryId);
            if (category == null) return ResponseBadRequest(new CustomBadRequest(localizer("CATEGORY_NOT_FOUND"), this.ControllerContext));

            Shop? shop = _shopRepository.FindByShopId(category.ShopId);
            if (shop == null) return ResponseBadRequest(new CustomBadRequest(localizer("SHOP_NOT_FOUND"), this.ControllerContext));
            if (shop.PartnerId != partner.PartnerId && shop.PartnerId != partner.ParentId) return ResponseBadRequest(new CustomBadRequest(localizer("CATEGORY_NOT_YOURS"), this.ControllerContext));
            if (string.IsNullOrEmpty(model.CategoryName)) return ResponseBadRequest(new CustomBadRequest(localizer("CATEGORY_NAME_INVALID"), this.ControllerContext));

            if (model.ParentId == "") return ResponseBadRequest(new CustomBadRequest(localizer("CATEGORY_PARENT_INVALID"), this.ControllerContext));
            if (!string.IsNullOrEmpty(model.ParentId))
            {
                var parentCategory = await _categoryRepository.FindByCategoryId(model.ParentId);
                if (parentCategory == null) return ResponseBadRequest(new CustomBadRequest(localizer("CATEGORY_PARENT_INVALID"), this.ControllerContext));
                if (parentCategory.CategoryType != model.CategoryType) return ResponseBadRequest(new CustomBadRequest(localizer("CATEGORY_PARENT_TYPE_INVALID"), this.ControllerContext));
            }

            category.ParentId = model.ParentId;
            category.CategoryName = model.CategoryName;
            category.CategoryDesc = model.CategoryDesc;
            category.CategoryPosition = model.CategoryPosition;
            category.Image = model.Image ?? new MediaInfo()
            {
                MediaFileId = "",
                Type = TypeMedia.IMAGE,
                Link = Constants.IsAlwaysUseS3 ? "sample/advertise.png" : "assets/sample/advertise.png"
            };
            category.IsHome = model.IsHome;
            category.Publish = model.Publish;
            category.Active = model.RuleActive;
            category.Updated = DateTimes.Now();
            category = await _categoryRepository.UpdateCategory(category);

            if (category.Image != null && !string.IsNullOrEmpty(category.Image.Link))
            {
                _groupFileRepository.UpdateRefIdByMediaFileId(category.ShopId, category.Image.MediaFileId, category.CategoryId);

                var existingImages = _groupFileRepository.DeleteMediaFilesByRefExcludeList(
                    category.ShopId,
                    category.CategoryId,
                    new List<string> { category.Image.MediaFileId }
                );
            }

            CategoryDto categoryDto = _mapper.Map<CategoryDto>(category);

            LogEvent(new EventLogDto
            {
                RefId = "",
                RefType = TypeFor.Partner,
                RequestId = requestId,
                Action = LogActionEnum.Update,
                Status = LogStatusEnum.Success,
                ActionAPI = $"{RoutePrefix.PARTNER}/CategoryPartner/UpdateCategory",
                Message = $"Partner update Category",
                Exception = null,
                DataObject = null
            });

            return ResponseData(categoryDto);
        }
        catch (Exception ex)
        {
            LogEvent(new EventLogDto
            {
                RefId = "",
                RefType = TypeFor.Partner,
                RequestId = requestId,
                Action = LogActionEnum.Update,
                Status = LogStatusEnum.Error,
                ActionAPI = $"{RoutePrefix.PARTNER}/CategoryPartner/UpdateCategory",
                Message = $"Error Partner update Category",
                Exception = ex,
                DataObject = null
            });

            return LogExceptionEvent(_log4net, $"{RoutePrefix.PARTNER}/CategoryPartner/UpdateCategory", ex, model);
        }
    }

    /// <summary>
    /// Delete Category for shop (Xoá danh mục cho cửa hàng)
    /// </summary>
    /// <param name="categoryId"></param>
    /// <returns>The result DeleteCategory for shop</returns>
    // DELETE: api/partner/CategoryPartner/DeleteCategory
    [HttpDelete("DeleteCategory")]
    [MultiPolicysAuthorizeAttribute(Policys = RolePrefix.Partner, Rules = "")]
    public async Task<IActionResult> DeleteCategory(string categoryId)
    {
        string requestId = Guid.NewGuid().ToString();
        try
        {
            string partnerId = GetUserIdAuth();
            Partner partner = await _partnerRepository.FindByPartnerId(partnerId);
            if (partner == null) return ResponseUnauthorized(new CustomBadRequest(localizer("BASE_USER_AUTH_NOT_FOUND"), this.ControllerContext));

            Category category = await _categoryRepository.FindByCategoryId(categoryId);
            if (category == null) return ResponseBadRequest(new CustomBadRequest(localizer("CATEGORY_NOT_FOUND"), this.ControllerContext));

            Shop? shop = _shopRepository.FindByShopId(category.ShopId);
            if (shop == null) return ResponseBadRequest(new CustomBadRequest(localizer("SHOP_NOT_FOUND"), this.ControllerContext));
            if (shop.PartnerId != partner.PartnerId && shop.PartnerId != partner.ParentId) return ResponseBadRequest(new CustomBadRequest(localizer("CATEGORY_NOT_YOURS"), this.ControllerContext));

            if (!string.IsNullOrEmpty(categoryId))
            {
                long totalItems = _itemsRepository.TotalItems(partnerId, shop.ShopId, categoryId);
                if (totalItems > 0) return ResponseBadRequest(new CustomBadRequest(localizer("CATEGORY_HAS_ITEMS"), this.ControllerContext));
            }

            //string defaultCategoryName = "Khác";
            //var defaultCategoryPaging = new Paging
            //{
            //    PageSize = 1,
            //    Search = defaultCategoryName
            //};

            //CategoryFilterDto filter = new CategoryFilterDto
            //{
            //    PartnerId = partner.PartnerId,
            //    ShopId = category.ShopId,
            //    CategoryType = category.CategoryType,
            //    Paging = defaultCategoryPaging
            //};

            //var categoriesResult = await _categoryRepository.ListCategory(filter);
            //Category defaultCategory = categoriesResult.Result.FirstOrDefault(c => c.CategoryName == defaultCategoryName);

            //if (defaultCategory == null)
            //{
            //    defaultCategory = new Category
            //    {
            //        CategoryId = Guid.NewGuid().ToString(),
            //        ParentId = null,
            //        PartnerId = partner.PartnerId,
            //        ShopId = shop.ShopId,
            //        CategoryType = category.CategoryType,
            //        CategoryName = defaultCategoryName,
            //        CategoryNameOrigin = defaultCategoryName.NonUnicode().ToLower(),
            //        CategoryLevel = "1",
            //        Image = new MediaInfo()
            //        {
            //            MediaFileId = "",
            //            Type = TypeMedia.IMAGE,
            //            Link = Constants.IsAlwaysUseS3 ? "sample/advertise.png" : "assets/sample/advertise.png"
            //        },
            //        CategoryDesc = "Default category for uncategorized items",
            //        CategoryPosition = 9999,
            //        IsHome = false,
            //        Publish = TypeCategoryPublish.Publish,
            //        Active = TypeRuleActive.Actived,
            //        Created = DateTimes.Now(),
            //        Updated = DateTimes.Now()
            //    };

            //    defaultCategory = await _categoryRepository.CreateCategory(defaultCategory);
            //}

            //var childCategoriesPaging = new Paging { PageSize = int.MaxValue };

            //CategoryFilterDto filterChild = new CategoryFilterDto
            //{
            //    PartnerId = partner.PartnerId,
            //    ShopId = category.ShopId,
            //    CategoryType = category.CategoryType,
            //    ParentId = category.CategoryId,
            //    Paging = childCategoriesPaging
            //};

            //var childCategoriesResult = await _categoryRepository.ListCategory(filterChild);

            //var childCategories = childCategoriesResult.Result;

            //if (childCategories != null && childCategories.Count > 0)
            //{
            //    foreach (var childCategory in childCategories)
            //    {
            //        childCategory.ParentId = null;
            //        childCategory.CategoryLevel = "1";
            //        childCategory.Updated = DateTimes.Now();
            //        await _categoryRepository.UpdateCategory(childCategory);
            //    }
            //}

            //var productsWithCategory = _itemsRepository.ListItems(new Paging { PageSize = int.MaxValue }, partnerId: null, shopId: null, categoryId: category.CategoryId);

            //if (productsWithCategory != null && productsWithCategory.Result.Count > 0)
            //{
            //    foreach (var product in productsWithCategory.Result)
            //    {
            //        product.CategoryIds.Remove(category.CategoryId);

            //        if (product.CategoryIds.Count == 0)
            //            product.CategoryIds.Add(defaultCategory.CategoryId);

            //        _itemsRepository.UpdateItems(product);
            //    }
            //}

            await S3Upload.DeleteImageS3(new List<string>() { category.Image?.Link ?? "" });
            await _categoryRepository.DeleteCategory(category.CategoryId);

            LogEvent(new EventLogDto
            {
                RefId = "",
                RefType = TypeFor.Partner,
                RequestId = requestId,
                Action = LogActionEnum.Delete,
                Status = LogStatusEnum.Success,
                ActionAPI = $"{RoutePrefix.PARTNER}/CategoryPartner/DeleteCategory",
                Message = $"Partner delete Category",
                Exception = null,
                DataObject = category
            });

            return ResponseData(new { Timestamp = DateTimes.Now(), Result = true, Message = localizer("CATEGORY_DELETE_SUCCESS") });
        }
        catch (Exception ex)
        {
            LogEvent(new EventLogDto
            {
                RefId = "",
                RefType = TypeFor.Partner,
                RequestId = requestId,
                Action = LogActionEnum.Delete,
                Status = LogStatusEnum.Error,
                ActionAPI = $"{RoutePrefix.PARTNER}/CategoryPartner/DeleteCategory/{categoryId}",
                Message = $"Error Partner delete Category",
                Exception = ex,
                DataObject = null
            });

            return LogExceptionEvent(_log4net, $"{RoutePrefix.PARTNER}/CategoryPartner/DeleteCategory", ex);
        }
    }

    /// <summary>
    /// Get detail Category (Lấy thông tin chi tiết bài viết cho cửa hảng)
    /// </summary>
    /// <param name="categoryId"></param>
    /// <returns>The result DetailCategory for shop</returns>
    // GET: api/partner/CategoryPartner/DetailCategory
    [HttpGet("DetailCategory")]
    public async Task<IActionResult> DetailCategory(string categoryId)
    {
        string requestId = Guid.NewGuid().ToString();
        try
        {
            string partnerId = GetUserIdAuth();
            Partner partner = await _partnerRepository.FindByPartnerId(partnerId);
            if (partner == null) return ResponseUnauthorized(new CustomBadRequest(localizer("BASE_USER_AUTH_NOT_FOUND"), this.ControllerContext));

            Category category = await _categoryRepository.FindByCategoryId(categoryId);
            if (category == null) return ResponseBadRequest(new CustomBadRequest(localizer("CATEGORY_NOT_FOUND"), this.ControllerContext));

            CategoryDto categoryDto = _mapper.Map<CategoryDto>(category);

            return ResponseData(categoryDto);
        }
        catch (Exception ex)
        {
            LogEvent(new EventLogDto
            {
                RefId = "",
                RefType = TypeFor.Partner,
                RequestId = requestId,
                Action = LogActionEnum.Load,
                Status = LogStatusEnum.Error,
                ActionAPI = $"{RoutePrefix.PARTNER}/CategoryPartner/DetailCategory",
                Message = $"Error Partner get detail Categor",
                Exception = ex,
                DataObject = null
            });

            return LogExceptionEvent(_log4net, $"{RoutePrefix.PARTNER}/CategoryPartner/DetailCategory", ex);
        }
    }

    /// <summary>
    /// Export Excel template for category import (Xuất file Excel mẫu để nhập dữ liệu danh mục)
    /// </summary>
    /// <returns>CSV file with template for user import</returns>
    // GET: api/partner/CategoryPartner/ExportTemplate
    [HttpGet("ExportTemplate")]
    public async Task<IActionResult> ExportTemplateCategory([FromQuery] string shopId, [FromQuery] TypeCategory categoryType)
    {
        string requestId = Guid.NewGuid().ToString();
        try
        {
            var fileBytes = await _categoryRepository.ExportTemplateCategory(shopId, categoryType);
            var fileName = categoryType == TypeCategory.Product ? $"TemplateCategoryProduct.xlsx" : $"TemplateCategoryService.xlsx";

            UploadStorageDto obj = new UploadStorageDto
            {
                FileBytes = fileBytes,
                PrefixPath = ExportConst.PATH_IMPORT,
                Type = TypeMedia.FILE,
                FileName = fileName
            };

            MediaFile objMedia = await _storageRepository.UploadFileAsync(obj);

            if (objMedia != null)
            {
                MediaFileDto objMediaDto = _mapper.Map<MediaFileDto>(objMedia);

                return ResponseData(new
                {
                    Timestamp = DateTimes.Now(),
                    Data = objMediaDto,
                    Message = localizer("SUCCESS")
                });
            }
            else
            {
                return ResponseData(new
                {
                    Timestamp = DateTimes.Now(),
                    Data = new MediaFileDto(),
                    Message = localizer("FAIL")
                });
            }
        }
        catch (Exception ex)
        {
            _log4net.Error($"Error Partner Export Template: {ex.Message}");

            LogEvent(new EventLogDto
            {
                RefId = "",
                RefType = TypeFor.Partner,
                RequestId = requestId,
                Action = LogActionEnum.Load,
                Status = LogStatusEnum.Error,
                ActionAPI = $"{RoutePrefix.API_PARTNER}/CategoryPartner/ExportTemplate",
                Message = $"Error Partner Export Template",
                Exception = ex,
                DataObject = null
            });

            return StatusCode(500, new
            {
                Timestamp = DateTime.Now,
                Message = localizer("INTERNAL_SERVER_ERROR"),
                Data = (object)null
            });
        }
    }


    /// <summary>
    /// Import users from XLSX file (Nhập dữ liệu người dùng từ file XLSX)
    /// </summary>
    /// <param name="file">XLSX file containing user data</param>
    /// <param name="shopId">ID of the shop</param>
    /// <param name="categoryType">Type Category</param>
    /// <returns>Result of the import process</returns>
    // POST: api/partner/CategoryPartner/ImportCategory
    [HttpPost("ImportCategory")]
    public async Task<IActionResult> ImportCategory([FromForm] IFormFile file, [FromForm] string shopId, [FromForm] TypeCategory categoryType)
    {
        string requestId = Guid.NewGuid().ToString();
        try
        {
            string partnerId = GetUserIdAuth();
            Partner partner = await _partnerRepository.FindByPartnerId(partnerId);
            if (partner == null)
                return ResponseUnauthorized(new CustomBadRequest(localizer("BASE_USER_AUTH_NOT_FOUND"), this.ControllerContext));

            if (file == null || file.Length == 0)
                return ResponseBadRequest(new CustomBadRequest(localizer("INVALID_TEMPLATE_FORMAT"), this.ControllerContext));

            if (!file.FileName.EndsWith(".xlsx"))
                return ResponseBadRequest(new CustomBadRequest(localizer("INVALID_TEMPLATE_FORMAT"), this.ControllerContext));

            if (string.IsNullOrEmpty(shopId))
                return ResponseBadRequest(new CustomBadRequest(localizer("NOT_FOUND"), this.ControllerContext));

            var list = new List<Category>();
            var errors = new List<string>();
            bool hasData = false;

            using (var stream = new MemoryStream())
            {
                await file.CopyToAsync(stream);
                using (var package = new ExcelPackage(stream))
                {
                    var worksheet = package.Workbook.Worksheets[1];

                    // Kiểm tra header
                    var headerRow = worksheet.Cells[1, 1, 1, ExportConst.HEADER_TEMPLATE_CATEGORY.Length];
                    for (int i = 0; i < ExportConst.HEADER_TEMPLATE_CATEGORY.Length; i++)
                    {
                        if (headerRow[1, i + 1].Text.Trim() != ExportConst.HEADER_TEMPLATE_CATEGORY[i])
                        {
                            return ResponseBadRequest(new CustomBadRequest(localizer("INVALID_TEMPLATE_FORMAT"), this.ControllerContext));
                        }
                    }

                    int rowCount = worksheet.Dimension.Rows;
                    for (int row = 2; row <= rowCount; row++)
                    {
                        try
                        {
                            string categoryName = worksheet.Cells[row, 1].Text.Trim();
                            string parentName = worksheet.Cells[row, 2].Text.Trim();
                            string categoryPositionText = worksheet.Cells[row, 3].Text.Trim();
                            string imageUrl = worksheet.Cells[row, 4].Text.Trim();

                            int? categoryPosition = null;

                            if (!string.IsNullOrEmpty(categoryPositionText) && int.TryParse(categoryPositionText, out int parsedPosition))
                                categoryPosition = parsedPosition;

                            // Validate dữ liệu bắt buộc
                            if (string.IsNullOrEmpty(categoryName))
                            {
                                errors.Add($"{localizer("ROW")} {row}: {localizer("CATEGORY_NAME")} {localizer("ROW_REQUIRED")}");
                                continue;
                            }

                            var obj = new CategoryDto
                            {
                                CategoryId = Guid.NewGuid().ToString(),
                                PartnerId = partner.ParentId != null ? partner.ParentId : partner.PartnerId,
                                ShopId = shopId,
                                CategoryType = categoryType,
                                CategoryName = categoryName,
                                ParentName = parentName,
                                CategoryPosition = categoryPosition ?? 0,
                                Publish = TypeCategoryPublish.Publish,
                                RuleActive = TypeRuleActive.Actived
                            };

                            if (!string.IsNullOrEmpty(parentName))
                            {
                                var parentCategory = await _categoryRepository.FindByCategoryName(parentName);

                                if (parentCategory != null)
                                    obj.ParentId = parentCategory.CategoryId;
                            }


                            if (!string.IsNullOrEmpty(imageUrl))
                            {
                                try
                                {
                                    UploadStorageDto objUpload = new UploadStorageDto
                                    {
                                        URL = imageUrl,
                                        PrefixPath = ExportConst.PATH_IMPORT,
                                        PrefixName = "Product",
                                        ContentType = "image/jpeg"
                                    };

                                    MediaFile objMedia = await _storageRepository.UploadImageUrlAsync(objUpload);

                                    if (objMedia != null)
                                        obj.Image = new MediaInfo()
                                        {
                                            MediaFileId = objMedia.MediaFileId,
                                            Type = TypeMedia.IMAGE,
                                            Link = objMedia.Link
                                        };
                                }
                                catch (Exception ex)
                                {
                                    _log4net.Error($"Lỗi khi tải ảnh từ URL {imageUrl}: {ex.Message}");
                                    errors.Add($"{localizer("ROW")} {row}: {localizer("IMAGE_DOWNLOAD_FAILED")} - {imageUrl}");
                                }
                            }

                            Category category = _mapper.Map<Category>(obj);
                            category.CategoryNameOrigin = categoryName;
                            category.IsHome = false;
                            category.CategoryDesc = string.Empty;

                            // if (string.IsNullOrEmpty(obj.Image?.MediaFileId))
                            //     category.Image = new MediaInfo()
                            //     {
                            //         MediaFileId = "",
                            //         Type = TypeMedia.IMAGE,
                            //         Link = Constants.IsAlwaysUseS3 ? "sample/advertise.png" : "assets/sample/advertise.png"
                            //     };

                            list.Add(category);
                            hasData = true;
                        }
                        catch (Exception ex)
                        {
                            _log4net.Error($"Lỗi xử lý dòng {row}: {ex.Message}");
                            errors.Add($"Dòng {row}: Lỗi xử lý dữ liệu - {ex.Message}");
                        }
                    }
                }
            }

            if (errors.Any())
            {
                return ResponseBadRequest(new CustomBadRequest(string.Join("\n", errors), this.ControllerContext));
            }

            if (!hasData)
            {
                return ResponseBadRequest(new CustomBadRequest(localizer("FILE_CONTAINS_NO_VALID_DATA"), this.ControllerContext));
            }

            var listResult = await _categoryRepository.CreateBulk(list);

            return ResponseData(new
            {
                Timestamp = DateTimes.Now(),
                Message = localizer("SUCCESS"),
                Data = new { CreatedCount = listResult.Count }
            });
        }
        catch (Exception ex)
        {
            LogEvent(new EventLogDto
            {
                RefId = "",
                RefType = TypeFor.Partner,
                RequestId = requestId,
                Action = LogActionEnum.Import,
                Status = LogStatusEnum.Error,
                ActionAPI = $"{RoutePrefix.API_PARTNER}/CategoryPartner/ImportCategory",
                Message = $"Error Partner Import ImportCategory",
                Exception = ex,
                DataObject = null
            });

            return StatusCode(500, new
            {
                Timestamp = DateTimes.Now(),
                Message = localizer("FAIL"),
                Data = (object)null
            });
        }
    }

    /// <summary>
    /// Export Excel Category for shop (Xuất Excel danh sách danh mục theo loại cho cửa hàng)
    /// </summary>
    /// <param name="model"></param>
    /// <returns>Result ListCategory for shop</returns>
    // GET: api/partner/CategoryPartner/ExportListCategory
    [HttpGet("exportlistcategory")]
    public async Task<IActionResult> ExportListCategory([FromQuery] CategoryFilterDto model)
    {
        string requestId = Guid.NewGuid().ToString();
        try
        {
            string partnerId = GetUserIdAuth();
            Partner partner = await _partnerRepository.FindByPartnerId(partnerId);

            if (partner == null)
                return ResponseUnauthorized(new CustomBadRequest(localizer("BASE_USER_AUTH_NOT_FOUND"), this.ControllerContext));

            Paging paging = new Paging()
            {
                Search = model.Paging?.Search ?? "",
                PageIndex = 0,
                PageSize = int.MaxValue,
                NameType = TypeSortName.Created,
                SortType = TypeSort.desc
            };

            partnerId = partner.ParentId != null ? partner.ParentId : partner.PartnerId;

            model.Paging = paging;
            model.PartnerId = partner.PartnerId;

            PagingResultBase<Category> listCategory = await _categoryRepository.ListCategory(model);
            List<CategoryDto> listCategoryDto = _mapper.Map<List<CategoryDto>>(listCategory.Result);
            List<string> ids = listCategoryDto.Select(x => x.ParentId).ToList();
            List<Category> listParent = await _categoryRepository.FindByCategoryIds(ids);

            foreach (var item in listCategoryDto)
            {
                item.ParentName = $"{(listParent.FirstOrDefault(x => x.CategoryId == item.ParentId)?.CategoryName ?? "")}";
                item.QuantityItems = _itemsRepository.TotalItems(categoryId: item.CategoryId);
            }

            if (listCategoryDto.Count > 0)
            {
                var fileBytes = await _categoryRepository.ExportListCategory(listCategoryDto);
                var fileName = model.CategoryType == TypeCategory.Product ? $"ListCategoryProduct.xlsx" : $"ListCategoryService.xlsx";

                UploadStorageDto obj = new UploadStorageDto
                {
                    FileBytes = fileBytes,
                    PrefixPath = ExportConst.PATH_IMPORT,
                    Type = TypeMedia.FILE,
                    FileName = fileName
                };

                MediaFile objMedia = await _storageRepository.UploadFileAsync(obj);

                if (objMedia != null)
                {
                    MediaFileDto objMediaDto = _mapper.Map<MediaFileDto>(objMedia);

                    return ResponseData(new
                    {
                        Timestamp = DateTimes.Now(),
                        Data = objMediaDto,
                        Message = localizer("SUCCESS")
                    });
                }
                else
                {
                    return ResponseData(new
                    {
                        Timestamp = DateTimes.Now(),
                        Data = new MediaFileDto(),
                        Message = localizer("FAIL")
                    });
                }
            }
            else
            {
                return ResponseData(new
                {
                    Timestamp = DateTimes.Now(),
                    Data = new MediaFileDto(),
                    Message = localizer("FAIL")
                });
            }
        }
        catch (Exception ex)
        {
            LogEvent(new EventLogDto
            {
                RefId = "",
                RefType = TypeFor.Partner,
                RequestId = requestId,
                Action = LogActionEnum.Export,
                Status = LogStatusEnum.Error,
                ActionAPI = $"{RoutePrefix.API_PARTNER}/CategoryPartner/ExportListCategory",
                Message = $"Error Partner Export ExportListCategory",
                Exception = ex,
                DataObject = null
            });

            return LogExceptionEvent(_log4net, $"{RoutePrefix.PARTNER}/CategoryPartner/ListCategory", ex);
        }
    }
}