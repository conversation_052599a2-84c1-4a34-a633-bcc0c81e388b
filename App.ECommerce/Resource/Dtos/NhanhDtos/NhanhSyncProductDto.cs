﻿using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace App.ECommerce.Resource.Dtos.NhanhDtos;

public class NhanhProductAttributeDto
{
    public string AttributeName { get; set; }
    public int Id { get; set; }
    public string Name { get; set; }
    public string Content { get; set; }
}

public class NhanhProductInventoryDepotDto
{
    public int Remain { get; set; }
    public int Shipping { get; set; }
    public int Holding { get; set; }
    public int Damage { get; set; }
    public int Available { get; set; }
}

public class NhanhProductInventoryDto
{
    public int Remain { get; set; }
    public int Shipping { get; set; }
    public int Holding { get; set; }
    public int Damage { get; set; }
    public int Available { get; set; }
    public Dictionary<string, NhanhProductInventoryDepotDto> Depots { get; set; }
}

public class NhanhProductWebhookDto
{
    public int ProductId { get; set; }
    public string ShopProductId { get; set; }
    public int CategoryId { get; set; }
    public int BrandId { get; set; }
    public int ParentId { get; set; }
    public string Code { get; set; }
    public string Barcode { get; set; }
    public string Name { get; set; }
    public double Price { get; set; }
    public int Vat { get; set; }
    public string Image { get; set; }
    public List<string> Images { get; set; }
    public string Status { get; set; }
    public string Description { get; set; }
    public string Content { get; set; }
    public float? Length { get; set; }
    public float? Width { get; set; }
    public float? Height { get; set; }
    public string CreatedDateTime { get; set; }
    public NhanhProductInventoryDto Inventories { get; set; }
    public List<NhanhProductAttributeDto> Attributes { get; set; }
    public double? Weight { get; set; }
}
public class NhanhProductCategoryDto
{
    public int Id { get; set; }
    public int ParentId { get; set; }
    public string Code { get; set; }
    public string Name { get; set; }
    public int Order { get; set; }
    public string Image { get; set; }
    public string Content { get; set; }
    public int Status { get; set; }
    public List<NhanhProductCategoryDto> Childs { get; set; }
}

/// <summary>
/// DTO cho response của API product detail từ Nhanh.vn
/// </summary>
public class NhanhProductDetailResponseDto
{
    /// <summary>
    /// Dictionary chứa danh sách sản phẩm với key là product ID
    /// </summary>
    [JsonProperty("data")]
    public Dictionary<string, NhanhProductDetailDto> Data { get; set; }
}

/// <summary>
/// Chi tiết sản phẩm từ API product detail của Nhanh.vn
/// </summary>
public class NhanhProductDetailDto
{
    [JsonProperty("idNhanh")]
    public int IdNhanh { get; set; }

    [JsonProperty("privateId")]
    public string PrivateId { get; set; }

    [JsonProperty("parentId")]
    public int ParentId { get; set; }

    [JsonProperty("merchantCategoryId")]
    public string MerchantCategoryId { get; set; }

    [JsonProperty("merchantProductId")]
    public string MerchantProductId { get; set; }

    [JsonProperty("categoryId")]
    public int CategoryId { get; set; }

    [JsonProperty("brandId")]
    public int? BrandId { get; set; }

    [JsonProperty("brandName")]
    public string BrandName { get; set; }

    [JsonProperty("code")]
    public string Code { get; set; }

    [JsonProperty("barcode")]
    public string Barcode { get; set; }

    [JsonProperty("name")]
    public string Name { get; set; }

    [JsonProperty("otherName")]
    public string OtherName { get; set; }

    [JsonProperty("importPrice")]
    public string ImportPrice { get; set; }

    [JsonProperty("oldPrice")]
    public string OldPrice { get; set; }

    [JsonProperty("price")]
    public string Price { get; set; }

    [JsonProperty("wholesalePrice")]
    public string WholesalePrice { get; set; }

    [JsonProperty("image")]
    public string Image { get; set; }

    [JsonProperty("unit")]
    public string Unit { get; set; }

    [JsonProperty("images")]
    public List<string> Images { get; set; }

    [JsonProperty("status")]
    public string Status { get; set; }

    [JsonProperty("previewLink")]
    public string PreviewLink { get; set; }

    [JsonProperty("advantages")]
    public string Advantages { get; set; }

    [JsonProperty("description")]
    public string Description { get; set; }

    [JsonProperty("content")]
    public string Content { get; set; }

    [JsonProperty("showHot")]
    public int ShowHot { get; set; }

    [JsonProperty("showNew")]
    public int ShowNew { get; set; }

    [JsonProperty("showHome")]
    public int ShowHome { get; set; }

    [JsonProperty("shippingWeight")]
    public string ShippingWeight { get; set; }

    [JsonProperty("width")]
    public string Width { get; set; }

    [JsonProperty("length")]
    public string Length { get; set; }

    [JsonProperty("height")]
    public string Height { get; set; }

    [JsonProperty("vat")]
    public string Vat { get; set; }

    [JsonProperty("createdDateTime")]
    public string CreatedDateTime { get; set; }

    [JsonProperty("inventory")]
    public NhanhProductDetailInventoryDto Inventory { get; set; }

    [JsonProperty("warranty")]
    public string Warranty { get; set; }

    [JsonProperty("warrantyAddress")]
    public string WarrantyAddress { get; set; }

    [JsonProperty("warrantyPhone")]
    public string WarrantyPhone { get; set; }

    [JsonProperty("typeId")]
    public string TypeId { get; set; }

    [JsonProperty("typeName")]
    public string TypeName { get; set; }

    [JsonProperty("attributes")]
    public List<Dictionary<string, NhanhProductDetailAttributeDto>> Attributes { get; set; }

    [JsonProperty("videos")]
    public List<string> Videos { get; set; }
}

/// <summary>
/// Thông tin inventory chi tiết từ API product detail
/// </summary>
public class NhanhProductDetailInventoryDto
{
    [JsonProperty("remain")]
    public int Remain { get; set; }

    [JsonProperty("shipping")]
    public int Shipping { get; set; }

    [JsonProperty("damaged")]
    public int Damaged { get; set; }

    [JsonProperty("holding")]
    public int Holding { get; set; }

    [JsonProperty("available")]
    public int Available { get; set; }

    [JsonProperty("warranty")]
    public int Warranty { get; set; }

    [JsonProperty("transfering")]
    public int Transfering { get; set; }

    [JsonProperty("warrantyHolding")]
    public int WarrantyHolding { get; set; }

    [JsonProperty("depots")]
    [JsonConverter(typeof(NhanhDepotsConverter))]
    public Dictionary<string, NhanhProductDetailDepotDto> Depots { get; set; }
}

/// <summary>
/// Thông tin depot từ API product detail
/// </summary>
public class NhanhProductDetailDepotDto
{
    [JsonProperty("remain")]
    public int Remain { get; set; }

    [JsonProperty("shipping")]
    public int Shipping { get; set; }

    [JsonProperty("damaged")]
    public int Damaged { get; set; }

    [JsonProperty("holding")]
    public int Holding { get; set; }

    [JsonProperty("warranty")]
    public int Warranty { get; set; }

    [JsonProperty("warrantyHolding")]
    public int WarrantyHolding { get; set; }

    [JsonProperty("transfering")]
    public int Transfering { get; set; }

    [JsonProperty("available")]
    public int Available { get; set; }
}

/// <summary>
/// Thông tin attribute chi tiết từ API product detail
/// </summary>
public class NhanhProductDetailAttributeDto
{
    [JsonProperty("attributeName")]
    public string AttributeName { get; set; }

    [JsonProperty("id")]
    public string Id { get; set; }

    [JsonProperty("name")]
    public string Name { get; set; }

    [JsonProperty("order")]
    public int Order { get; set; }
}

/// <summary>
/// Custom JsonConverter để xử lý trường depots có thể là array [] hoặc object {}
/// </summary>
public class NhanhDepotsConverter : JsonConverter<Dictionary<string, NhanhProductDetailDepotDto>>
{
    public override Dictionary<string, NhanhProductDetailDepotDto> ReadJson(JsonReader reader, Type objectType, Dictionary<string, NhanhProductDetailDepotDto> existingValue, bool hasExistingValue, JsonSerializer serializer)
    {
        var token = JToken.Load(reader);

        if (token.Type == JTokenType.Array)
        {
            // Nếu là array (thường là array rỗng []), trả về dictionary rỗng
            return new Dictionary<string, NhanhProductDetailDepotDto>();
        }
        else if (token.Type == JTokenType.Object)
        {
            // Nếu là object, deserialize bình thường
            return token.ToObject<Dictionary<string, NhanhProductDetailDepotDto>>(serializer);
        }
        else
        {
            // Trường hợp khác, trả về dictionary rỗng
            return new Dictionary<string, NhanhProductDetailDepotDto>();
        }
    }

    public override void WriteJson(JsonWriter writer, Dictionary<string, NhanhProductDetailDepotDto> value, JsonSerializer serializer)
    {
        // Serialize bình thường
        serializer.Serialize(writer, value);
    }
}
