using System;

namespace App.ECommerce.Resource.Dtos.ResultDtos;

public class TotalOrderStatusDto
{
    public long? All { get; set; } = 0;
    public long? Pendding { get; set; } = 0;
    public long? WaitingForDelivery { get; set; } = 0;
    public long? Delivering { get; set; } = 0;
    public long? Success { get; set; } = 0;
    public long? Failed { get; set; } = 0;
    public long? Refund { get; set; } = 0;
}
